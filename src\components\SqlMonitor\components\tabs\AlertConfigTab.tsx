import React, { useState, useEffect } from 'react';
import { Button, Space, Table, Drawer, App, Popconfirm, Tooltip } from 'antd';
import { SendOutlined, CheckOutlined, ClearOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

// 导入重构后的模块
import type { TaskAlert } from '../../types';
import { formStyles, tableStyles } from '../../styles';
import AlertTable from '../AlertTable';

interface AlertConfigTabProps {
  alerts: TaskAlert[];
  onAlertsChange: (alerts: TaskAlert[]) => void;
  onAddAlert: () => void;
  onEditAlert: (index: number) => void;
}

/**
 * 监控项配置标签页组件
 * 管理监控项的配置
 */
const AlertConfigTab: React.FC<AlertConfigTabProps> = ({ alerts, onAlertsChange, onEditAlert }) => {
  const { message } = App.useApp();

  // 动态高度状态
  const [containerHeight, setContainerHeight] = useState(600);

  // 抽屉状态管理
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRowsFromChild, setSelectedRowsFromChild] = useState<TaskAlert[]>([]);

  // 计算动态高度
  useEffect(() => {
    const calculateHeight = () => {
      // 100vh - 60(头部) - 84(底部) - 48(card padding: 24*2) - 30(tab切换栏)
      const dynamicHeight = window.innerHeight - 60 - 85 - 48 - 30;
      setContainerHeight(Math.max(400, dynamicHeight)); // 最小高度400px
    };

    // 初次加载时计算
    calculateHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateHeight);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);

  const handleDeleteAlert = (index: number) => {
    const alertToDelete = alerts[index];
    const newAlerts = alerts.filter((_, i) => i !== index);
    onAlertsChange(newAlerts);
    message.success(`已移除监控项"${alertToDelete.name}"`);
  };

  // 清空所有选择
  const handleClearAll = () => {
    onAlertsChange([]);
    message.success('已清空所有选择');
  };

  // 处理选择监控项按钮点击
  const handleSelectAlert = () => {
    setDrawerVisible(true);
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  // 处理抽屉内的选择确认
  const handleDrawerSelectionConfirm = () => {
    console.log('确认选择时的selectedRows:', selectedRowsFromChild);

    onAlertsChange(selectedRowsFromChild);

    handleDrawerClose();
    message.success(`已选择 ${selectedRowsFromChild.length} 个监控项`);
  };

  // 处理选择变化（从AlertTable传递过来）
  const handleSelectionChange = (selectedAlerts: TaskAlert[]) => {
    console.log('AlertConfigTab 接收到选择变化:', selectedAlerts);
    setSelectedRowsFromChild(selectedAlerts);
  };

  const columns: ColumnsType<TaskAlert> = [
    {
      title: '监控项名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
    },
    {
      title: '监控项级别',
      dataIndex: 'severity',
      key: 'severity',
      width: 120,
      render: (severity: string) => <span className={`severity-${severity}`}>{severity}</span>,
    },
    {
      title: '监控项类型',
      dataIndex: 'alert_type',
      key: 'alert_type',
      width: 120,
    },
    {
      title: 'SQL语句',
      dataIndex: 'sql',
      key: 'sql',
      ellipsis: true,
      render: (sql: string) => (
        <code className="sql-code" title={sql}>
          {sql}
        </code>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: string, record: TaskAlert, index: number) => (
        <Space size="small">
          <Button type="text" size="small" icon={<EyeOutlined />} onClick={() => onEditAlert(index)} className="hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
            查看
          </Button>
          <Popconfirm title="确认移除" description={`确定要移除监控项"${record.name}"吗？`} onConfirm={() => handleDeleteAlert(index)} okText="确认" cancelText="取消" placement="topRight">
            <Button type="text" size="small" danger icon={<DeleteOutlined />} className="hover:bg-red-50 hover:text-red-600 transition-colors duration-200">
              移除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={`${formStyles.tabContent} relative flex flex-col border border-gray-200 bg-white`} style={{ height: `${containerHeight}px` }}>
      {/* 固定顶部 - 标题和操作按钮 */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-5 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <h4 className="m-0 text-lg font-medium">关联监控项</h4>
            <span className="text-gray-500 text-sm">共 {alerts.length} 个配置</span>
          </div>
          <Space>
            {alerts.length > 0 && (
              <Button danger size="small" type="text" icon={<ClearOutlined />} onClick={handleClearAll}>
                清空选择
              </Button>
            )}
            <Button size="small" type="text" icon={<SendOutlined />} onClick={handleSelectAlert}>
              选择监控项
            </Button>
          </Space>
        </div>
      </div>

      {/* 可滚动的中间内容区域 */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50" style={{ maxHeight: `calc(${containerHeight}px)` }}>
        {alerts.length === 0 ? (
          <div className="text-center py-20 px-8 text-gray-400 bg-gradient-to-br from-gray-50 to-slate-50  border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors duration-300">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
              <SendOutlined className="text-2xl text-gray-400" />
            </div>
            <div className="text-lg mb-2 font-medium text-gray-600">暂无监控项配置</div>
            <div className="text-sm text-gray-500">请点击"选择监控项"添加配置</div>
          </div>
        ) : (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <Table
              dataSource={alerts}
              rowKey="id"
              size="small"
              pagination={false}
              columns={columns}
              scroll={{ y: containerHeight - 120 }} // 表格内容区域滚动高度（不包括表头和顶部部操作栏）
              className={`custom-alert-table ${tableStyles['custom-alert-table']}`}
              rowClassName={(_record, index) => `hover:bg-blue-50 transition-colors duration-200 ${index % 2 === 0 ? 'bg-gray-50/30' : 'bg-white'}`}
              components={{
                header: {
                  cell: (props: React.HTMLAttributes<HTMLTableCellElement>) => (
                    <th {...props} className={`${props.className} bg-gradient-to-r from-gray-50 to-gray-100 font-semibold text-gray-700 border-b-2 border-gray-200`} />
                  ),
                },
              }}
            />
          </div>
        )}
      </div>

      {/* 选择监控项抽屉 */}
      <Drawer
        title={
          <div className="flex justify-between items-center w-full">
            <span>选择监控项配置</span>
            <Button type="primary" icon={<CheckOutlined />} onClick={handleDrawerSelectionConfirm}>
              确认选择 {selectedRowsFromChild.length > 0 && `(${selectedRowsFromChild.length})`}
            </Button>
          </div>
        }
        width="80%"
        open={drawerVisible}
        onClose={handleDrawerClose}
        maskClosable={false}
      >
        <AlertTable contentHeight={containerHeight} isSelectionMode={true} selectedRows={alerts} onSelectionConfirm={handleSelectionChange} />
      </Drawer>
    </div>
  );
};

export default AlertConfigTab;
